#import "WhaleDetailVC.h"
#import "AddAttentionModel.h"
#import "AttentionTableViewNewCell.h"
#import "DoLikeModel.h"
#import "OperationTableViewCell.h"
#import "OperationsModel.h"
#import "OtherUserInfo.h"
#import "PopoverView.h"
#import "ReleaseAttentionModel.h"
#import "UserAffairListModel.h"
#import "WhaleHeaderView.h"

#import "BlackUserModel.h"
#import "LiuyanDetailVC.h"
#import "LiuyanModel.h"
#import "LiuyanVC.h"
#import "ReportModel.h"
#import "ReportVC.h"
#import "TagAffairVC.h"
#import "_2hz-Swift.h"

#define WhaleDetailVCID @"WhaleDetailVCID"

static NSString *const kCellStyleDidChangeNotification =
    @"CellStyleDidChangeNotification";
@interface WhaleDetailVC () <UITableViewDelegate, UITableViewDataSource,
                             NicknameDelegate, otherUserActionDelegate,
                             MomentDelegate> {
  NSString *attentionType;
  int currentPage;
}

@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) NSMutableArray *cellData;
@property(nonatomic, strong) OtherUserPersonResult *userInfo;
@property(nonatomic, strong) otherUserHeadView *headView;
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UIImageView *titleAvatarView;
@property(nonatomic, strong) UIButton *backButton;
@property(nonatomic, strong) UIButton *messageButton;
@property(nonatomic, strong) UIView *containerView;

@end

@implementation WhaleDetailVC

- (void)loadUserData:(NSInteger)type {
  __weak typeof(self) wSelf = self;
  [OtherUserInfo getOtherUserInfo:self.uid
      success:^(NSDictionary *resultObject) {
        OtherUserInfo *member =
            [OtherUserInfo mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          wSelf.userInfo = [OtherUserPersonResult
              mj_objectWithKeyValues:member.data.firstObject];
          self->attentionType = wSelf.userInfo.attentionRs;
          [wSelf loadtableView];

          BOOL shouldHideDynamics =
              wSelf.userInfo.homeHide &&
              ![CurrentUser.userid isEqualToString:wSelf.uid];

          if (shouldHideDynamics) {
            [wSelf.cellData removeAllObjects];
            [wSelf.tableView reloadData];
          } else {
            if (type == 1 && wSelf.userInfo.special == 0) {
              [wSelf loadData];
            } else if (wSelf.userInfo.special == 1) {
              [wSelf loadDataSpecial];
            }
          }

          [wSelf.titleAvatarView
              sd_setImageWithURL:[NSURL URLWithString:wSelf.userInfo.avatar]];

          dispatch_async(dispatch_get_main_queue(), ^{
            wSelf.tableView.tableHeaderView = wSelf.tableView.tableHeaderView;
            [wSelf.tableView setNeedsLayout];
            [wSelf.tableView layoutIfNeeded];
          });
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)loadDataSpecial {
  BOOL shouldHideDynamics =
      _userInfo.homeHide && ![CurrentUser.userid isEqualToString:self.uid];

  if (shouldHideDynamics) {
    return;
  }

  __weak typeof(self) wSelf = self;
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  }
  if (currentPage == 1) {
    [wSelf.cellData removeAllObjects];
  }

  [OperationsModel
      getUserSpecialAffairListModel:[NSString
                                        stringWithFormat:@"%d", currentPage]
      size:@"20"
      success:^(NSDictionary *resultObject) {
        if ([resultObject[@"code"] intValue] == 0) {
          NSArray *dataArray = resultObject[@"data"];
          if (dataArray.count > 0) {
            NSDictionary *firstPage = dataArray.firstObject;
            NSArray *items = firstPage[@"data"];

            if (items.count > 0) {
              NSMutableArray *modelArray = [NSMutableArray array];
              for (NSDictionary *item in items) {
                OperationItemResult *model =
                    [OperationItemResult mj_objectWithKeyValues:item];
                [modelArray addObject:model];
              }

              [wSelf.cellData addObjectsFromArray:modelArray];

              NSString *totalCount = firstPage[@"count"];
              if ([totalCount integerValue] > wSelf.cellData.count) {
                if (!wSelf.tableView.mj_footer) {
                  MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                      footerWithRefreshingTarget:self
                                refreshingAction:@selector(loadMoreData)];
                  footer.refreshingTitleHidden = YES;
                  wSelf.tableView.mj_footer = footer;
                }
              } else {
                [wSelf.tableView.mj_footer removeFromSuperview];
                wSelf.tableView.mj_footer = nil;
              }

              dispatch_async(dispatch_get_main_queue(), ^{
                [wSelf.tableView reloadData];
              });
            }
          }
        }

        [wSelf.tableView.mj_footer endRefreshing];
      }
      failure:^(NSError *requestErr) {
        [wSelf.tableView.mj_footer endRefreshing];
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)loadData {
  BOOL shouldHideDynamics =
      _userInfo.homeHide && ![CurrentUser.userid isEqualToString:self.uid];

  if (shouldHideDynamics) {
    return;
  }

  __weak typeof(self) wSelf = self;
  if (!_cellData) {
    _cellData = [NSMutableArray new];
  }
  if (currentPage == 1) {
    [wSelf.cellData removeAllObjects];
  }

  [UserAffairListModel getUserAffairListModel:self.uid
      page:[NSString stringWithFormat:@"%d", currentPage]
      size:@"20"
      success:^(NSDictionary *resultObject) {
        AffairListModel *member =
            [AffairListModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          AffairListModelResult *tempMember = [AffairListModelResult
              mj_objectWithKeyValues:member.data.firstObject];
          for (int i = 0; i < tempMember.data.count; i++) {
            DynamicModelResult *dy = [DynamicModelResult
                mj_objectWithKeyValues:[tempMember.data objectAtIndex:i]];
            [wSelf.cellData addObject:dy];
          }
          if ([tempMember.count integerValue] > wSelf.cellData.count) {
            if (!self.tableView.mj_footer) {
              MJChiBaoZiFooter *footer = [MJChiBaoZiFooter
                  footerWithRefreshingTarget:self
                            refreshingAction:@selector(loadMoreData)];
              footer.refreshingTitleHidden = YES;
              self.tableView.mj_footer = footer;
            }
          } else {
            [self.tableView.mj_footer removeFromSuperview];
            self.tableView.mj_footer = nil;
          }

        } else {
        }
        [wSelf.tableView.mj_footer endRefreshing];
        [wSelf.tableView reloadData];
      }
      failure:^(NSError *requestErr) {
        [wSelf.tableView.mj_footer endRefreshing];
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)loadReportData:(NSString *)report_type detail:(NSString *)detail {
  __weak typeof(self) wSelf = self;
  [ReportModel postReportModel:self.uid
      report_type:report_type
      type:@"2"
      rid:self.uid
      detail:detail
      success:^(NSDictionary *resultObject) {
        ReportModel *member = [ReportModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"举报成功"];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"举报失败,请重试"];
      }];
}

- (void)loadLaheiUser {
  __weak typeof(self) wSelf = self;
  [BlackUserModel postBlackUserModel:self.uid
      success:^(NSDictionary *resultObject) {
        BlackUserModel *member =
            [BlackUserModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          [wSelf showToastFast:@"拉黑成功"];
          [wSelf performSelector:@selector(backAction)
                      withObject:nil
                      afterDelay:1.0];
        } else {
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"拉黑失败,请重试"];
      }];
}
#pragma mark Life Cycle
- (void)viewDidLoad {
  [super viewDidLoad];
  self.tableView.estimatedRowHeight = 200;
  self.tableView.rowHeight = UITableViewAutomaticDimension;
  currentPage = 1;

  [[NSNotificationCenter defaultCenter]
      addObserver:self
         selector:@selector(cellStyleDidChange:)
             name:kCellStyleDidChangeNotification
           object:nil];

  [self loadUserData:1];
}

- (void)loadMoreData {
  BOOL shouldHideDynamics =
      _userInfo.homeHide && ![CurrentUser.userid isEqualToString:self.uid];

  if (shouldHideDynamics) {
    return;
  }

  currentPage += 1;
  [self loadUserData:1];
}

- (void)loadtableView {
  self.view.backgroundColor = KColor_HighBlack;
  if (!_tableView) {
    @weakify(self);
    _backButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
    _backButton.adjustsImageWhenHighlighted = YES;
    [_backButton setImage:[UIImage imageNamed:@"back_white"]
                 forState:(UIControlStateNormal)];
    [_backButton addTarget:self
                    action:@selector(backAction)
          forControlEvents:(UIControlEventTouchUpInside)];
    [self.safeContentView addSubview:_backButton];
    [self.backButton mas_makeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.left.top.equalTo(self.safeContentView).offset(24 * kWidthFactor);
      make.width.mas_equalTo(10 * kWidthFactor);
      make.height.mas_equalTo(15 * kWidthFactor);
    }];

    UIView *titleContainer = [[UIView alloc] init];
    [self.safeContentView addSubview:titleContainer];

    _titleLabel =
        [[UILabel alloc] initWithFrame:CGRectMake(0, 0, kMainWidth * 0.5, 34)];
    _titleLabel.text = self.userInfo.nickname;
    _titleLabel.font = SourceHanSerifBoldFont(14 * kWidthFactor);
    _titleLabel.textColor = KColor_HighBlue;
    _titleLabel.textAlignment = NSTextAlignmentCenter;
    _titleLabel.alpha = 0;
    [titleContainer addSubview:_titleLabel];

    _titleAvatarView = [[UIImageView alloc] init];
    _titleAvatarView.contentMode = UIViewContentModeScaleAspectFill;
    _titleAvatarView.clipsToBounds = YES;
    _titleAvatarView.layer.cornerRadius = 12 * kWidthFactor;
    _titleAvatarView.alpha = 0;
    [titleContainer addSubview:_titleAvatarView];

    [titleContainer mas_makeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.centerX.equalTo(self.safeContentView);
      make.centerY.equalTo(self.backButton);
    }];

    [self.titleAvatarView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(titleContainer);
      make.centerY.equalTo(titleContainer);
      make.size.mas_equalTo(CGSizeMake(24 * kWidthFactor, 24 * kWidthFactor));
    }];

    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.titleAvatarView.mas_right)
          .offset(8 * kWidthFactor);
      make.right.equalTo(titleContainer);
      make.centerY.equalTo(titleContainer);
    }];

    if (![CurrentUser.userid isEqualToString:self.uid]) {
      if (![self.uid isEqualToString:@"108474"]) {
        _messageButton = [UIButton buttonWithType:(UIButtonTypeCustom)];
        [_messageButton setImage:[UIImage imageNamed:@"more-white"]
                        forState:(UIControlStateNormal)];
        _messageButton.adjustsImageWhenHighlighted = YES;
        [_messageButton addTarget:self
                           action:@selector(gotoMessagePage:)
                 forControlEvents:(UIControlEventTouchUpInside)];
        [self.safeContentView addSubview:_messageButton];
        [self.messageButton mas_makeConstraints:^(MASConstraintMaker *make) {
          @strongify(self);
          make.right.equalTo(self.safeContentView).offset(-24 * kWidthFactor);
          make.centerY.equalTo(self.backButton);
        }];
      }
    }

    _containerView = [[UIView alloc] init];
    UIImageView *pullDownImage = [[UIImageView alloc] init];
    pullDownImage.image = KImage_name(@"Bigf");
    pullDownImage.contentMode = UIViewContentModeScaleAspectFit;
    [_containerView addSubview:pullDownImage];

    [pullDownImage mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerX.equalTo(_containerView);
      make.bottom.equalTo(_containerView.mas_top).offset(50);
      make.size.mas_equalTo(CGSizeMake(kMainWidth * 0.6, kMainWidth * 0.6));
    }];

    _headView = [[otherUserHeadView alloc] init];
    _headView.delegate = self;
    if ([CurrentUser.userid isEqualToString:self.uid]) {
      [_headView updateUnreadBadge:NO];
    }
    [_containerView addSubview:_headView];

    [_headView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.right.bottom.equalTo(_containerView);
      make.top.equalTo(_containerView.mas_top).offset(-1000);
    }];

    _headView.otherUserInfo = self.userInfo;

    [_headView setNeedsLayout];
    [_headView layoutIfNeeded];
    CGFloat headerHeight = [_headView getContentHeight];

    [_headView mas_updateConstraints:^(MASConstraintMaker *make) {
      make.height.equalTo(@(headerHeight));
    }];

    [_containerView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.width.equalTo(@(kMainWidth));
    }];

    _tableView = [[multiColorTableView alloc] initWithFrame:CGRectZero];
    _tableView.delegate = self;
    _tableView.dataSource = self;
    _tableView.bounces = YES;
    _tableView.tableHeaderView = _containerView;

    _tableView.tableHeaderView = _tableView.tableHeaderView;
    [_tableView registerClass:[AttentionTableViewNewCell class]
        forCellReuseIdentifier:WhaleDetailVCID];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;

    UIView *bgView = [UIView new];
    bgView.backgroundColor = UIColor.whiteColor;
    [self.safeContentView addSubview:bgView];
    [bgView mas_makeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.right.left.equalTo(self.safeContentView);
      make.top.equalTo(self.backButton.mas_bottom).offset(20);
    }];

    [self.safeContentView addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
      @strongify(self);
      make.right.left.equalTo(self.safeContentView);
      make.top.equalTo(self.backButton.mas_bottom).offset(20);
      make.bottom.equalTo(self.view);
    }];
  } else {
  }

  if (@available(iOS 11.0, *)) {
    _tableView.contentInsetAdjustmentBehavior =
        UIScrollViewContentInsetAdjustmentNever;
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    [self.tableView setNeedsLayout];
    [self.tableView layoutIfNeeded];
  });
}

#pragma mark - Lazy

- (void)backAction {
  [self.navigationController popViewControllerAnimated:YES];
}

- (void)gotoMessagePage:(UIButton *)sender {
  PopoverView *popoverView = [PopoverView popoverView];
  popoverView.showShade = YES;
  [popoverView showToView:sender withActions:[self QQActions]];
}

- (NSArray<PopoverAction *> *)QQActions {
  PopoverAction *multichatAction = [PopoverAction
      actionWithImage:[UIImage imageNamed:@"Report"]
                title:@"举报"
              handler:^(PopoverAction *action) {
                ReportVC *ddVC = [[ReportVC alloc] init];
                ddVC.delegate = self;
                [self.navigationController pushViewController:ddVC
                                                     animated:YES];
              }];
  PopoverAction *addFriAction = [PopoverAction
      actionWithImage:[UIImage imageNamed:@"more-lahei"]
                title:@"拉黑"
              handler:^(PopoverAction *action) {
                UIAlertController *alertController = [UIAlertController
                    alertControllerWithTitle:@"确定拉黑该用户?"
                                     message:@"拉黑后你们将互相看不到对方的动态"
                                             @"。如需解除，请在设置-"
                                             @"黑名单管理中移除黑名单。"
                              preferredStyle:UIAlertControllerStyleAlert];
                UIAlertAction *cancelAction =
                    [UIAlertAction actionWithTitle:@"取消"
                                             style:UIAlertActionStyleCancel
                                           handler:nil];
                __weak typeof(self) wSelf = self;
                UIAlertAction *okAction = [UIAlertAction
                    actionWithTitle:@"确定"
                              style:UIAlertActionStyleDefault
                            handler:^(UIAlertAction *_Nonnull action) {
                              [wSelf loadLaheiUser];
                            }];

                [alertController addAction:cancelAction];
                [alertController addAction:okAction];
                [self presentViewController:alertController
                                   animated:YES
                                 completion:nil];
              }];

  if (_userInfo.special > 0) {
    return @[ addFriAction ];
  }

  return @[ multichatAction, addFriAction ];
}

#pragma mark -otherheadview delegate

- (void)followAction {
  if ([self.uid isEqualToString:CurrentUser.userid]) {
    [self showToastFast:@"🐳无法关注自己"];
  } else {
    __weak typeof(self) wSelf = self;
    if ([attentionType isEqualToString:@"0"]) {
      [AddAttentionModel postAddAttentionModel:self.uid
          success:^(NSDictionary *resultObject) {
            AddAttentionModel *member =
                [AddAttentionModel mj_objectWithKeyValues:resultObject];
            if ([member.success boolValue]) {
              [wSelf showToastFast:@"关注成功"];
              [self loadUserData:2];
              [NOTIFICENTER postNotificationName:MyUserAffReload object:nil];
            } else if ([member.code integerValue] ==
                       FTHZErrorCodeAccountBanned) {
              ShowBanTip(member.msg);
            } else {
              [wSelf showToastFast:member.msg];
            }
          }
          failure:^(NSError *requestErr) {
            [wSelf showToastFast:@"数据有误,请检查网络后重试"];
          }];
    } else if ([attentionType isEqualToString:@"1"]) {

      UIAlertController *alertController = [UIAlertController
          alertControllerWithTitle:@"提示"
                           message:@"确认取消关注？"
                    preferredStyle:UIAlertControllerStyleAlert];
      UIAlertAction *cancelAction =
          [UIAlertAction actionWithTitle:@"取消"
                                   style:UIAlertActionStyleCancel
                                 handler:nil];
      __weak typeof(self) wSelf = self;
      UIAlertAction *okAction = [UIAlertAction
          actionWithTitle:@"确定"
                    style:UIAlertActionStyleDefault
                  handler:^(UIAlertAction *_Nonnull action) {
                    [ReleaseAttentionModel postReleaseAttentionModel:self.uid
                        success:^(NSDictionary *resultObject) {
                          ReleaseAttentionModel *member = [ReleaseAttentionModel
                              mj_objectWithKeyValues:resultObject];
                          if ([member.success boolValue]) {
                            [wSelf showToastFast:@"取消关注成功"];
                            [NOTIFICENTER postNotificationName:MyUserAffReload
                                                        object:nil];
                            [self loadUserData:2];
                          } else if ([member.code integerValue] ==
                                     FTHZErrorCodeAccountBanned) {
                            ShowBanTip(member.msg);
                          } else {
                            [wSelf showToastFast:member.msg];
                          }
                        }
                        failure:^(NSError *requestErr) {
                          [wSelf showToastFast:@"数据有误,请检查网络后重试"];
                        }];
                  }];
      [alertController addAction:cancelAction];
      [alertController addAction:okAction];
      [self presentViewController:alertController animated:YES completion:nil];
    } else if ([attentionType isEqualToString:@"2"]) {
      UIAlertController *alertController = [UIAlertController
          alertControllerWithTitle:@"提示"
                           message:@"确认取消关注？"
                    preferredStyle:UIAlertControllerStyleAlert];
      UIAlertAction *cancelAction =
          [UIAlertAction actionWithTitle:@"取消"
                                   style:UIAlertActionStyleCancel
                                 handler:nil];
      __weak typeof(self) wSelf = self;
      UIAlertAction *okAction = [UIAlertAction
          actionWithTitle:@"确定"
                    style:UIAlertActionStyleDefault
                  handler:^(UIAlertAction *_Nonnull action) {
                    [ReleaseAttentionModel postReleaseAttentionModel:self.uid
                        success:^(NSDictionary *resultObject) {
                          ReleaseAttentionModel *member = [ReleaseAttentionModel
                              mj_objectWithKeyValues:resultObject];
                          if ([member.success boolValue]) {
                            [wSelf showToastFast:@"取消关注成功"];
                            [NOTIFICENTER postNotificationName:MyUserAffReload
                                                        object:nil];
                            [self loadUserData:2];
                          } else if ([member.code integerValue] ==
                                     FTHZErrorCodeAccountBanned) {
                            ShowBanTip(member.msg);
                          } else {
                            [wSelf showToastFast:member.msg];
                          }
                        }
                        failure:^(NSError *requestErr) {
                          [wSelf showToastFast:@"数据有误,请检查网络后重试"];
                        }];
                  }];
      [alertController addAction:cancelAction];
      [alertController addAction:okAction];
      [self presentViewController:alertController animated:YES completion:nil];
    }
  }
}

- (void)messageAction {
  LiuyanVC *ddVC = [[LiuyanVC alloc] init];
  ddVC.to = self.uid;

  [self.navigationController pushViewController:ddVC animated:YES];
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 50 * kWidthFactor;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  BOOL shouldHideDynamics =
      _userInfo.homeHide && ![CurrentUser.userid isEqualToString:self.uid];

  if (shouldHideDynamics) {
    return kMainHeight - 400 * kWidthFactor;
  }

  if (_cellData.count == 0) {
    return _userInfo.special == 0 ? 96 * kWidthFactor : 0;
  }
  return 0;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  BOOL shouldHideDynamics =
      _userInfo.homeHide && ![CurrentUser.userid isEqualToString:self.uid];

  if (shouldHideDynamics) {
    UIView *containerView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, kMainWidth,
                                 kMainHeight - 400 * kWidthFactor)];
    containerView.backgroundColor = UIColor.clearColor;

    UIView *hideView = [[UIView alloc] init];
    hideView.backgroundColor = UIColor.clearColor;
    [containerView addSubview:hideView];

    [hideView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.top.equalTo(containerView).offset(20 * kWidthFactor);
      make.centerX.equalTo(containerView);
      make.width.height.equalTo(@(60 * kWidthFactor));
    }];

    UIImageView *homeHideImageView = [[UIImageView alloc] init];
    homeHideImageView.image = [UIImage imageNamed:@"homeHide"];
    homeHideImageView.contentMode = UIViewContentModeScaleAspectFit;
    [hideView addSubview:homeHideImageView];

    [homeHideImageView mas_makeConstraints:^(MASConstraintMaker *make) {
      make.center.equalTo(hideView);
      make.width.height.equalTo(@(45 * kWidthFactor));
    }];

    return containerView;
  }

  if (_cellData.count == 0 && _userInfo.special == 0) {
    UIView *bgView = [[UIView alloc]
        initWithFrame:CGRectMake(0, 0, kMainWidth, 96 * kWidthFactor)];
    UILabel *tipL = [UILabel new];
    tipL.text = @"这是一只沉默的小鲸鱼";
    tipL.font = SourceHanSerifMediumFont(12 * kWidthFactor);
    tipL.textColor = KColor_textTinyGray;
    [bgView addSubview:tipL];
    [tipL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.centerX.bottom.equalTo(bgView);
    }];
    return bgView;
  }
  return nil;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  BOOL shouldHideDynamics =
      _userInfo.homeHide && ![CurrentUser.userid isEqualToString:self.uid];

  if (shouldHideDynamics) {
    return 0;
  }

  return _cellData.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (self.userInfo.special > 0) {
    OperationTableViewCell *cell =
        [tableView dequeueReusableCellWithIdentifier:@"OperationCellID"];
    if (!cell) {
      cell = [[OperationTableViewCell alloc]
            initWithStyle:UITableViewCellStyleDefault
          reuseIdentifier:@"OperationCellID"];
    }

    OperationItemResult *item = self.cellData[indexPath.row];
    [cell setupWithItem:item];
    return cell;
  } else {
    DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
    AttentionTableViewNewCell *cell = (AttentionTableViewNewCell *)[tableView
        dequeueReusableCellWithIdentifier:WhaleDetailVCID
                             forIndexPath:indexPath];
    if (!cell) {
      cell = [[AttentionTableViewNewCell alloc]
            initWithStyle:(UITableViewCellStyleDefault)
          reuseIdentifier:WhaleDetailVCID];
    }
    [cell setDynamic:dy showTime:YES];
    [cell.awesomeBtn addTarget:self
                        action:@selector(onTouchBtnInCell:)
              forControlEvents:(UIControlEventTouchUpInside)];
    cell.commentBtn.tag = indexPath.row;
    [cell.commentBtn addTarget:self
                        action:@selector(onTouchContentCell:)
              forControlEvents:(UIControlEventTouchUpInside)];

    return cell;
  }
}

- (void)onTouchOperationCell:(UITapGestureRecognizer *)tap {
  NSInteger index = tap.view.tag;
  OperationItemResult *item = self.cellData[index];
}

- (void)onTouchBtnTagInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  TagAffairVC *ddVC = [[TagAffairVC alloc] init];
  ddVC.tagId = dy.affair.tagType;
  ddVC.tagName = dy.affair.tagName;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)onTouchBtnInCell:(UIButton *)sender {
  CGPoint point = sender.center;
  point = [self.tableView convertPoint:point fromView:sender.superview];
  NSIndexPath *indexpath = [self.tableView indexPathForRowAtPoint:point];
  DynamicModelResult *dy = [_cellData objectAtIndex:indexpath.row];
  [self doLike:dy.affair.aid uid:dy.user.uid indexRow:indexpath.row];
}

- (void)onTouchContentCell:(UIButton *)sender {
  DynamicModelResult *dy = [_cellData objectAtIndex:sender.tag];
  FZMomentVC *ddVC = [[FZMomentVC alloc] init];
  ddVC.userId = dy.user.uid;
  ddVC.momentId = dy.affair.aid;
  ddVC.withComment = YES;
  [self.navigationController pushViewController:ddVC animated:YES];
}

- (void)doLike:(NSString *)aid
           uid:(NSString *)uid
      indexRow:(NSInteger)indexRow {
  DynamicModelResult *dy = [_cellData objectAtIndex:indexRow];
  __weak typeof(self) wSelf = self;
  void (^action)(NSDictionary *) = ^(NSDictionary *resultObject) {
    DoLikeModel *member = [DoLikeModel mj_objectWithKeyValues:resultObject];
    if ([member.success boolValue]) {
      if ([dy.affair.likeRs isEqualToString:@"0"]) {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike++;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        dy.affair.likeRs = @"1";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      } else {
        NSInteger tepLike = [dy.affair.likeNum integerValue];
        tepLike--;
        dy.affair.likeNum = [NSString stringWithFormat:@"%ld", tepLike];
        dy.affair.likeRs = @"0";
        NSMutableArray *newArray = [wSelf.cellData mutableCopy];
        [newArray replaceObjectAtIndex:indexRow withObject:dy];
        wSelf.cellData = newArray;
      }
      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:indexRow
                                                  inSection:0];
      [wSelf.tableView
          reloadRowsAtIndexPaths:[NSArray arrayWithObjects:indexPath, nil]
                withRowAnimation:UITableViewRowAnimationNone];
    } else if ([member.code integerValue] == FTHZErrorCodeAccountBanned) {
      ShowBanTip(member.msg);
    } else {
      [wSelf.view makeToast:member.msg
                   duration:1.0
                   position:CSToastPositionCenter];
    }
  };
  if ([dy.affair upvoted]) {
    [DoLikeModel postUnlikeModel:uid
                       contentid:aid
                         success:action
                         failure:^(NSError *requestErr) {
                           [self.view makeToast:@"数据有误,请检查网络后重试"
                                       duration:2.0
                                       position:CSToastPositionCenter];
                         }];
  } else {
    [DoLikeModel postLikeModel:uid
                     contentid:aid
                       success:action
                       failure:^(NSError *requestErr) {
                         [self.view makeToast:@"数据有误,请检查网络后重试"
                                     duration:2.0
                                     position:CSToastPositionCenter];
                       }];
  }
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
  if (self.userInfo.special > 0) {
    OperationItemResult *item = self.cellData[indexPath.row];
    FZOperationDetailVC *detailVC = [[FZOperationDetailVC alloc] init];
    detailVC.operationId = item.id.stringValue;
    [self.navigationController pushViewController:detailVC animated:YES];
  } else {
    DynamicModelResult *dy = [_cellData objectAtIndex:indexPath.row];
    FZMomentVC *ddVC = [[FZMomentVC alloc] init];
    ddVC.userId = dy.user.uid;
    ddVC.momentId = dy.affair.aid;
    ddVC.withComment = NO;
    ddVC.delegate = self;
    [self.navigationController pushViewController:ddVC animated:YES];
  }
}

- (void)momentDeleted:(NSIndexPath *)index {
  if (index.row < _cellData.count) {
    DynamicModelResult *dynamic = [_cellData objectAtIndex:index.row];
    dynamic.affair.status =
        [dynamic.affair.status isEqualToString:@"2"] ? @"0" : @"2";
    [self.tableView reloadRowsAtIndexPaths:@[ index ]
                          withRowAnimation:UITableViewRowAnimationNone];
  }
}

- (void)needRefreshList {
  BOOL shouldHideDynamics =
      _userInfo.homeHide && ![CurrentUser.userid isEqualToString:self.uid];

  if (shouldHideDynamics) {
    return;
  }

  [self loadData];
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
  CGFloat currentOffsetY = scrollView.contentOffset.y;
  if (currentOffsetY < 0) {
    CGFloat maxScale = 1.5f;
    CGFloat scale = MIN(maxScale, MAX(1.0f, 1.0f - currentOffsetY / 300.0f));

    self.headView.avatarBgView.transform =
        CGAffineTransformMakeScale(scale, scale);
  } else {
    self.headView.avatarBgView.transform = CGAffineTransformIdentity;
  }

  if (currentOffsetY >= 380 * kWidthFactor) {
    [AppConfig statusbarStyle:YES];
    self.view.backgroundColor = UIColor.whiteColor;
    [_messageButton setImage:[UIImage imageNamed:@"more-black"]
                    forState:UIControlStateNormal];
    [_backButton setImage:[UIImage imageNamed:@"back"]
                 forState:UIControlStateNormal];
    self.titleLabel.alpha = 1;
    self.titleAvatarView.alpha = 1;
  } else {
    self.view.backgroundColor = KColor_HighBlack;
    [UIApplication sharedApplication].statusBarStyle =
        UIStatusBarStyleLightContent;
    [_messageButton setImage:[UIImage imageNamed:@"more-white"]
                    forState:UIControlStateNormal];
    [_backButton setImage:[UIImage imageNamed:@"back_white"]
                 forState:UIControlStateNormal];
    self.titleLabel.alpha = 0;
    self.titleAvatarView.alpha = 0;
  }
}
- (void)fadeOtherElements:(CGFloat)alpha {
  for (UIView *subview in self.headView.contentView.subviews) {
    if (subview != self.headView.avatarBgView) {
      subview.alpha = alpha;
    }
  }

  self.headView.profileBottomView.alpha = alpha;
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:YES animated:NO];
  [UIApplication sharedApplication].statusBarStyle =
      UIStatusBarStyleLightContent;
  [self scrollViewDidScroll:self.tableView];
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];
  [AppConfig statusbarStyle:YES];
}

- (void)delegateGetReport:(NSInteger)RelationshipType
                   detail:(NSString *)detail {
  [self loadReportData:[NSString stringWithFormat:@"%ld", RelationshipType]
                detail:detail];
}

- (void)cellStyleDidChange:(NSNotification *)notification {
  dispatch_async(dispatch_get_main_queue(), ^{
    [self.tableView beginUpdates];
    [self.tableView endUpdates];

    dispatch_after(
        dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)),
        dispatch_get_main_queue(), ^{
          [self.tableView reloadData];
        });
  });
}

- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end

@implementation multiColorTableView

- (void)drawRect:(CGRect)rect {
  [KColor_White set];
  UIRectFill(rect);
}

@end
