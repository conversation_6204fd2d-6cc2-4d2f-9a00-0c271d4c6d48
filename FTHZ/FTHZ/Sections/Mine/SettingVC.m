#import "SettingVC.h"
#import "ActivityWebVC.h"
#import "BindMobileVC.h"
#import "BlackUserVC.h"
#import "CellStyleModel.h"
#import "ChangeMobileVC.h"
#import "ChangePasswordVC.h"
#import "FTHZLocationManager.h"
#import "HomeHideModel.h"
#import "IPStatusModel.h"
#import "LoginBusiness.h"
#import "POSTIPStatusModel.h"
#import "SettingTableViewCell.h"
#import "UserUserinfoModel.h"
#import "WhaleDetailVC.h"

#define SettingTableViewCellID @"SettingTableViewCellID"

static NSString *const kCellStyleDidChangeNotification =
    @"CellStyleDidChangeNotification";
@interface SettingVC () <UITableViewDelegate, UITableViewDataSource>
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) NSArray *dataSource;
@property(nonatomic, strong) IPStatusModelResult *tempSet;
@property(nonatomic, copy) NSString *cacheSizeString;
@property(nonatomic, strong) CLLocation *currentLocation;
@property(nonatomic, copy) NSString *currentLocationName;
@end

@implementation SettingVC
- (void)loadData {
  __weak typeof(self) wSelf = self;
  [IPStatusModel
      getIPStatusModel:^(NSDictionary *resultObject) {
        IPStatusModel *member =
            [IPStatusModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          wSelf.tempSet = [IPStatusModelResult
              mj_objectWithKeyValues:member.data.firstObject];

          if (wSelf.tempSet.cellStyle) {
            [[NSUserDefaults standardUserDefaults]
                setObject:wSelf.tempSet.cellStyle
                   forKey:@"userCellStyle"];
            [[NSUserDefaults standardUserDefaults] synchronize];
          }

          if (wSelf.tableView.window) {
            [wSelf.tableView reloadData];
          }
        }
      }
               failure:^(NSError *requestErr){
               }];
}

- (void)loadSet:(NSInteger)type {
  __weak typeof(self) wSelf = self;
  [HUD show];

  NSString *pushStatus = self.tempSet.pushStatus;
  NSString *locationStatus = self.tempSet.locationStatus;

  switch (type) {
  case 1:
    pushStatus = @"0";
    break;
  case 2:
    pushStatus = @"1";
    break;
  case 3:
    locationStatus = @"0";
    break;
  case 4:
    locationStatus = @"1";
    break;
  }

  [POSTIPStatusModel postPOSTIPStatusModel:locationStatus
      pushStatus:pushStatus
      success:^(NSDictionary *resultObject) {
        POSTIPStatusModel *member =
            [POSTIPStatusModel mj_objectWithKeyValues:resultObject];
        [HUD dissmiss];

        if ([member.success boolValue]) {
          wSelf.tempSet.pushStatus = pushStatus;
          wSelf.tempSet.locationStatus = locationStatus;

          if (type == 3 || type == 4) {
            CurrentUserConfig.enableLocation =
                [locationStatus isEqualToString:@"0"];
          }

          [wSelf.tableView reloadData];
        } else {
          [wSelf showToastFast:member.msg];
          if (type == 3 || type == 4) {
            CurrentUserConfig.enableLocation =
                ![locationStatus isEqualToString:@"0"];
          }
          [wSelf.tableView reloadData];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
        if (type == 3 || type == 4) {
          CurrentUserConfig.enableLocation =
              ![locationStatus isEqualToString:@"0"];
        }
        [wSelf.tableView reloadData];
      }];
}

- (void)getUserHeziInfo {
  @weakify(self);
  [AccountManager syncUserInfoWithCompletion:^(UserPersonResult *_Nullable user,
                                               NSError *_Nullable error) {
    [HUD dissmiss];
    @strongify(self);
    if (error) {
      [self showToastFast:error.localizedDescription];
      return;
    }
    [self loadData];
  }];
}

- (void)handleMobileBinding {
  __weak typeof(self) wSelf = self;

  if (self.tempSet.haveMobile && self.tempSet.haveMobile.length > 0) {
    ChangeMobileVC *changeMobileVC = [[ChangeMobileVC alloc] init];
    changeMobileVC.currentMobile = self.tempSet.haveMobile;
    changeMobileVC.didChangeSuccess = ^{
      [wSelf loadData];
    };
    [self.navigationController pushViewController:changeMobileVC animated:YES];
  } else {
    BindMobileVC *bindMobileVC = [[BindMobileVC alloc] init];
    bindMobileVC.didBindSuccess = ^{
      [wSelf loadData];
    };
    [self.navigationController pushViewController:bindMobileVC animated:YES];
  }
}

- (void)viewDidLoad {
  [super viewDidLoad];
  self.title = @"设置";
  self.view.backgroundColor = [UIColor whiteColor];
  _dataSource = @[
    @"展示地理位置", @"图片展示样式", @"隐藏主页内容", @"黑名单管理",
    @"修改密码", @"绑定手机号", @"联系管理员", @"清理缓存"
  ];

  @weakify(self);
  [self actionCustomLeftBtnWithNrlImage:@"back"
                               htlImage:@"back"
                                  title:@""
                                 action:^{
                                   @strongify(self);
                                   [self.navigationController
                                       popViewControllerAnimated:YES];
                                   [[NSNotificationCenter defaultCenter]
                                       postNotificationName:ReopenSideMenu
                                                     object:nil];
                                 }];

  self.tempSet = [[IPStatusModelResult alloc] init];
  self.tempSet.locationStatus = @"1";
  self.cacheSizeString = @"计算中...";

  [self setupViews];

  [self preloadLocationIfAuthorized];
}

- (void)setupViews {
  UIView *containerView = [[UIView alloc] init];
  [self.view addSubview:containerView];
  [containerView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);
    make.left.right.bottom.equalTo(self.view);
  }];

  self.tableView = [[UITableView alloc] init];
  self.tableView.delegate = self;
  self.tableView.dataSource = self;
  self.tableView.backgroundColor = [UIColor clearColor];
  self.tableView.showsVerticalScrollIndicator = NO;
  [self.tableView registerClass:[SettingTableViewCell class]
         forCellReuseIdentifier:SettingTableViewCellID];
  self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
  [containerView addSubview:self.tableView];

  FlatButton *loginBtn = [FlatButton buttonWithType:UIButtonTypeSystem];
  loginBtn.backgroundColor = KColor_HighBlack;
  [loginBtn setTitle:@"退出账号" forState:UIControlStateNormal];
  loginBtn.titleLabel.font = SourceHanSerifMediumFont(16 * kMainTemp);
  [loginBtn setTitleColor:KColor_White forState:UIControlStateNormal];
  UIImage *logoutImage = [KImage_name(@"logout")
      imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
  [loginBtn setImage:logoutImage forState:UIControlStateNormal];
  [loginBtn setImageEdgeInsets:UIEdgeInsetsMake(0, -10 * kMainTemp, 0, 0)];
  loginBtn.layer.cornerRadius = 30 * kMainTemp;
  loginBtn.layer.masksToBounds = YES;
  [loginBtn addTarget:self
                action:@selector(loginOut)
      forControlEvents:UIControlEventTouchUpInside];
  [containerView addSubview:loginBtn];

  UILabel *versionLabel = [[UILabel alloc] init];
  versionLabel.textColor = KColor_detailLightGray;
  NSDictionary *infoDic = [[NSBundle mainBundle] infoDictionary];
  NSString *appVersion = [infoDic objectForKey:@"CFBundleShortVersionString"];
  versionLabel.text = [NSString stringWithFormat:@"Version %@", appVersion];
  versionLabel.font = SourceHanSerifRegularFont(12 * kMainTemp);
  [containerView addSubview:versionLabel];

  UILabel *zoeVersionLabel = [[UILabel alloc] init];
  zoeVersionLabel.textColor = KColor_detailLightGray;
  zoeVersionLabel.text = @"回归版";
  zoeVersionLabel.font = SourceHanSerifRegularFont(12 * kMainTemp);
  [containerView addSubview:zoeVersionLabel];

  [loginBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(containerView.mas_safeAreaLayoutGuideBottom)
        .offset(-70 * kMainTemp);
    make.centerX.equalTo(containerView);
    make.size.mas_equalTo(CGSizeMake(160 * kMainTemp, 60 * kMainTemp));
  }];

  [versionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(loginBtn.mas_bottom).offset(20 * kMainTemp);
    make.centerX.equalTo(containerView);
  }];

  [zoeVersionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(versionLabel.mas_bottom).offset(4 * kMainTemp);
    make.centerX.equalTo(containerView);
    make.bottom.lessThanOrEqualTo(containerView.mas_bottom)
        .offset(-10 * kMainTemp);
  }];

  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(containerView);
    make.left.right.equalTo(containerView);
    make.bottom.equalTo(loginBtn.mas_top).offset(-20 * kMainTemp);
  }];

  [self.view layoutIfNeeded];
}

- (void)calculateCacheSize {
  NSString *cachePath = [NSSearchPathForDirectoriesInDomains(
      NSCachesDirectory, NSUserDomainMask, YES) firstObject];
  NSString *tempPath = NSTemporaryDirectory();

  float cacheSize =
      [self folderSizeAtPath:cachePath] + [self folderSizeAtPath:tempPath];

  NSString *sizeString;
  if (cacheSize < 1024) {
    sizeString = [NSString stringWithFormat:@"%.2fKB", cacheSize];
  } else {
    sizeString = [NSString stringWithFormat:@"%.2fMB", cacheSize / 1024.0];
  }

  dispatch_async(dispatch_get_main_queue(), ^{
    self.cacheSizeString = sizeString;
    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:7 inSection:0];
    [self.tableView reloadRowsAtIndexPaths:@[ indexPath ]
                          withRowAnimation:UITableViewRowAnimationNone];
  });
}

- (float)folderSizeAtPath:(NSString *)path {
  NSFileManager *fileManager = [NSFileManager defaultManager];
  float folderSize = 0.0;

  if (![fileManager fileExistsAtPath:path]) {
    return 0;
  }
  NSArray *childerFiles = [fileManager subpathsAtPath:path];
  for (NSString *fileName in childerFiles) {
    NSString *fileAbsolutePath = [path stringByAppendingPathComponent:fileName];
    folderSize += [self fileSizeAtPath:fileAbsolutePath];
  }

  return folderSize / 1024.0;
}

- (long long)fileSizeAtPath:(NSString *)path {
  NSFileManager *fileManager = [NSFileManager defaultManager];
  if ([fileManager fileExistsAtPath:path]) {
    return [[fileManager attributesOfItemAtPath:path error:nil] fileSize];
  }
  return 0;
}

- (void)clearCache {
  NSString *cachePath = [NSSearchPathForDirectoriesInDomains(
      NSCachesDirectory, NSUserDomainMask, YES) firstObject];
  NSString *tempPath = NSTemporaryDirectory();

  NSFileManager *fileManager = [NSFileManager defaultManager];

  NSArray *cacheFiles = [fileManager subpathsAtPath:cachePath];
  for (NSString *fileName in cacheFiles) {
    NSString *fileAbsolutePath =
        [cachePath stringByAppendingPathComponent:fileName];
    [fileManager removeItemAtPath:fileAbsolutePath error:nil];
  }

  NSArray *tempFiles = [fileManager subpathsAtPath:tempPath];
  for (NSString *fileName in tempFiles) {
    NSString *fileAbsolutePath =
        [tempPath stringByAppendingPathComponent:fileName];
    [fileManager removeItemAtPath:fileAbsolutePath error:nil];
  }

  [self calculateCacheSize];
  [self showToastFast:@"清理完成"];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [self.navigationController setNavigationBarHidden:NO animated:YES];
  self.showBackBtn = YES;

  [AppConfig statusbarStyle:YES];
  dispatch_async(dispatch_get_main_queue(), ^{
    [self loadData];
  });
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];

  dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0),
                 ^{
                   [self calculateCacheSize];
                 });
}

- (void)viewWillDisappear:(BOOL)animated {
  [super viewWillDisappear:animated];

  [LocationManager endUpdateLocationWithIdentifier:@"SettingVCPreload"];
  [LocationManager
      endUpdateLocationWithIdentifier:NSStringFromClass([self class])];
}

- (void)loginOut {
  UIAlertController *alertController =
      [UIAlertController alertControllerWithTitle:@"提示"
                                          message:@"确定退出登录?"
                                   preferredStyle:UIAlertControllerStyleAlert];
  UIAlertAction *cancelAction =
      [UIAlertAction actionWithTitle:@"取消"
                               style:UIAlertActionStyleCancel
                             handler:nil];
  __weak typeof(self) wSelf = self;
  UIAlertAction *okAction = [UIAlertAction
      actionWithTitle:@"确定"
                style:UIAlertActionStyleDefault
              handler:^(UIAlertAction *_Nonnull action) {
                [AccountManager
                    logoutWithCompletion:^(UserPersonResult *_Nullable user,
                                           NSError *_Nullable error) {
                      if (error) {
                        [self showToastFast:error.localizedDescription];
                        return;
                      }
                      [FTHZBusiness
                          loginThenAction:^(FTHZLoginResult result,
                                            UserPersonResult *_Nullable user,
                                            NSError *_Nullable error){

                          }];
                    }];
                [NOTIFICENTER postNotificationName:SettingLoginOut object:nil];
                self.navigationController.tabBarController.selectedIndex = 0;
                [wSelf.navigationController
                    popToRootViewControllerAnimated:YES];
              }];
  [alertController addAction:cancelAction];
  [alertController addAction:okAction];
  [self presentViewController:alertController animated:YES completion:nil];
}

#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 10.0;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 0.01f;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  return 60 * kMainTemp;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _dataSource.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  SettingTableViewCell *cell = (SettingTableViewCell *)[tableView
      dequeueReusableCellWithIdentifier:SettingTableViewCellID
                           forIndexPath:indexPath];
  if (!cell) {
    cell = [[SettingTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:SettingTableViewCellID];
  }
  cell.mainSwitch.hidden = NO;
  [cell loadSetting:indexPath.row];
  if (indexPath.row == 5) {
    if (self.tempSet.haveMobile && self.tempSet.haveMobile.length > 0) {
      cell.nameLabel.text = @"改绑手机号";
    } else {
      cell.nameLabel.text = @"绑定手机号";
    }
  } else {
    cell.nameLabel.text =
        [NSString stringWithFormat:@"%@", _dataSource[indexPath.row]];
  }

  NSArray *iconNames = @[
    @"dingwei", @"tupianyangshi", @"yincangzhuye", @"heimingdan", @"xiugaimima",
    @"bangdingshouji", @"guanliyuan", @"huancun"
  ];
  if (indexPath.row < iconNames.count) {
    cell.iconImageView.image = [UIImage imageNamed:iconNames[indexPath.row]];
  }
  [cell.mainSwitch removeTarget:nil
                         action:NULL
               forControlEvents:UIControlEventValueChanged];
  if (indexPath.row == 0) {
    cell.mainSwitch.hidden = NO;
    [cell.mainSwitch setOn:[self locationIsAvalilable] animated:NO];
    cell.mainSwitch.tag = indexPath.row;
    [cell.mainSwitch addTarget:self
                        action:@selector(switchAction:)
              forControlEvents:UIControlEventValueChanged];
  } else {
    cell.mainSwitch.hidden = YES;
  }
  if (indexPath.row == 2) {
    cell.mainSwitch.hidden = NO;
    [cell.mainSwitch setOn:[self.tempSet.homeHide isEqualToString:@"1"]
                  animated:NO];
    cell.mainSwitch.tag = indexPath.row;
    [cell.mainSwitch addTarget:self
                        action:@selector(homeHideSwitchAction:)
              forControlEvents:UIControlEventValueChanged];
    [cell.mainSwitch mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(cell.contentView.mas_right).offset(-24 * kMainTemp);
      make.centerY.equalTo(cell.nameLabel);
    }];
  }
  if (indexPath.row == 7) {
    cell.mainSwitch.hidden = YES;
    cell.detailLabel.text = self.cacheSizeString;
    cell.detailLabel.hidden = NO;
  }
  if (indexPath.row == 1) {
    [cell.leftButton removeTarget:nil
                           action:NULL
                 forControlEvents:UIControlEventTouchUpInside];
    [cell.rightButton removeTarget:nil
                            action:NULL
                  forControlEvents:UIControlEventTouchUpInside];

    [cell.leftButton addTarget:self
                        action:@selector(leftButtonClicked:)
              forControlEvents:UIControlEventTouchUpInside];
    [cell.rightButton addTarget:self
                         action:@selector(rightButtonClicked:)
               forControlEvents:UIControlEventTouchUpInside];

    if ([self.tempSet.cellStyle isEqualToString:@"1"]) {
      cell.leftButton.selected = YES;
      cell.leftButton.backgroundColor = KColor_HighBlack;
      cell.rightButton.selected = NO;
      cell.rightButton.backgroundColor = [UIColor clearColor];
    } else if ([self.tempSet.cellStyle isEqualToString:@"2"]) {
      cell.rightButton.selected = YES;
      cell.rightButton.backgroundColor = KColor_HighBlack;
      cell.leftButton.selected = NO;
      cell.leftButton.backgroundColor = [UIColor clearColor];
    } else {
      cell.leftButton.selected = NO;
      cell.leftButton.backgroundColor = [UIColor clearColor];
      cell.rightButton.selected = NO;
      cell.rightButton.backgroundColor = [UIColor clearColor];
    }
  }

  if (indexPath.row >= 3 && indexPath.row <= 6) {
    if (!cell.arrowImageView) {
      UIImage *arrowImage = [[UIImage imageNamed:@"goto"]
          imageWithRenderingMode:UIImageRenderingModeAlwaysTemplate];
      cell.arrowImageView = [[UIImageView alloc] initWithImage:arrowImage];
      cell.arrowImageView.tintColor = KColor_detailLightGray;
      [cell.contentView addSubview:cell.arrowImageView];
      [cell.arrowImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(cell.contentView);
        make.right.equalTo(cell.contentView).offset(-25 * kMainTemp);
        make.size.mas_equalTo(CGSizeMake(4 * kMainTemp, 8 * kMainTemp));
      }];
    }
  }
  return cell;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  [tableView deselectRowAtIndexPath:indexPath animated:YES];
  switch (indexPath.row) {
  case 0:
    break;
  case 1: {
    break;
  }
  case 2: {
    break;
  }
  case 3: {
    BlackUserVC *blackUserVC = [[BlackUserVC alloc] init];
    [self.navigationController pushViewController:blackUserVC animated:YES];
  } break;
  case 4: {
    ChangePasswordVC *changePasswordVC = [[ChangePasswordVC alloc] init];
    [self.navigationController pushViewController:changePasswordVC
                                         animated:YES];
  } break;
  case 5: {
    [self handleMobileBinding];
  } break;
  case 6: {
    WhaleDetailVC *whaleDetailVC = [[WhaleDetailVC alloc] init];
    whaleDetailVC.uid = @"108474";
    [self.navigationController pushViewController:whaleDetailVC animated:YES];
  } break;
  case 7: {
    UIAlertController *alert = [UIAlertController
        alertControllerWithTitle:@"提示"
                         message:@"确定要清理缓存吗？"
                  preferredStyle:UIAlertControllerStyleAlert];

    [alert addAction:[UIAlertAction actionWithTitle:@"取消"
                                              style:UIAlertActionStyleCancel
                                            handler:nil]];
    [alert addAction:[UIAlertAction
                         actionWithTitle:@"确定"
                                   style:UIAlertActionStyleDefault
                                 handler:^(UIAlertAction *_Nonnull action) {
                                   [self clearCache];
                                 }]];
    [self presentViewController:alert animated:YES completion:nil];
  } break;
  }
}

- (BOOL)locationIsAvalilable {
  CLAuthorizationStatus status = [LocationManager status];
  if (self.tempSet.locationStatus == nil ||
      [self.tempSet.locationStatus isEqualToString:@""]) {
    return NO;
  }
  switch (status) {
  case kCLAuthorizationStatusDenied:
  case kCLAuthorizationStatusRestricted:
  case kCLAuthorizationStatusNotDetermined:
    return NO;
  case kCLAuthorizationStatusAuthorizedAlways:
  case kCLAuthorizationStatusAuthorizedWhenInUse:
    return [self.tempSet.locationStatus isEqualToString:@"0"];
  }
  return NO;
}

- (void)switchAction:(UISwitch *)whichSwitch {
  if (whichSwitch.tag == 0) {
    [self locationSwitchChanged:whichSwitch];
  }
}

- (void)homeHideSwitchAction:(UISwitch *)whichSwitch {
  if (whichSwitch.tag == 2) {
    [self homeHideSwitchChanged:whichSwitch];
  }
}

- (void)locationSwitchChanged:(UISwitch *)aSwitch {
  if (aSwitch.isOn) {
    switch ([LocationManager status]) {
    case kCLAuthorizationStatusAuthorizedWhenInUse:
    case kCLAuthorizationStatusAuthorizedAlways:
      CurrentUserConfig.enableLocation = YES;
      [self updateLocationAndSave:3];
      break;
    case kCLAuthorizationStatusNotDetermined: {
      dispatch_async(dispatch_get_main_queue(), ^{
        [LocationManager
            requestAuthorizationWithCompletion:^(CLAuthorizationStatus status) {
              dispatch_async(dispatch_get_main_queue(), ^{
                switch (status) {
                case kCLAuthorizationStatusDenied:
                case kCLAuthorizationStatusRestricted:
                case kCLAuthorizationStatusNotDetermined:
                  [aSwitch setOn:NO animated:YES];
                  [self showToastFast:@"请在设置中开启定位权限"];
                  break;

                case kCLAuthorizationStatusAuthorizedAlways:
                case kCLAuthorizationStatusAuthorizedWhenInUse:
                  CurrentUserConfig.enableLocation = YES;
                  [self updateLocationAndSave:3];
                  break;
                }
              });
            }];
      });
    } break;
    case kCLAuthorizationStatusRestricted:
    case kCLAuthorizationStatusDenied: {
      [aSwitch setOn:NO animated:YES];
      UIAlertController *alert = [UIAlertController
          alertControllerWithTitle:@"开启定位"
                           message:
                               @"需要在系统设置中开启定位权限，是否前往设置？"
                    preferredStyle:UIAlertControllerStyleAlert];
      [alert addAction:[UIAlertAction actionWithTitle:@"取消"
                                                style:UIAlertActionStyleCancel
                                              handler:nil]];
      [alert
          addAction:
              [UIAlertAction
                  actionWithTitle:@"设置"
                            style:UIAlertActionStyleDefault
                          handler:^(UIAlertAction *_Nonnull action) {
                            [[UIApplication sharedApplication]
                                openURL:
                                    [NSURL
                                        URLWithString:
                                            UIApplicationOpenSettingsURLString]];
                          }]];
      [self presentViewController:alert animated:YES completion:nil];
      break;
    }
    }
  } else {
    CurrentUserConfig.enableLocation = NO;
    [self loadSet:4];
  }
}

- (void)updateLocationAndSave:(NSInteger)type {
  @weakify(self);
  [HUD show];
  if (self.currentLocation) {
    NSString *locationName = self.currentLocationName;
    NSString *pushStatus = self.tempSet.pushStatus;
    NSString *locationStatus = @"0";
    [POSTIPStatusModel postPOSTIPStatusModel:locationStatus
        pushStatus:pushStatus
        location:locationName
        success:^(NSDictionary *resultObject) {
          [HUD dissmiss];
          POSTIPStatusModel *member =
              [POSTIPStatusModel mj_objectWithKeyValues:resultObject];
          if ([member.success boolValue]) {
            self.tempSet.locationStatus = locationStatus;
            self.tempSet.pushStatus = pushStatus;
            CurrentUserConfig.enableLocation = YES;
            [self.tableView reloadData];
          } else {
            [self showToastFast:member.msg];
            CurrentUserConfig.enableLocation = NO;
            [self.tableView reloadData];
          }
        }
        failure:^(NSError *requestErr) {
          [HUD dissmiss];
          [self showToastFast:@"数据有误,请检查网络后重试"];
          CurrentUserConfig.enableLocation = NO;
          [self.tableView reloadData];
        }];
    return;
  }

  dispatch_after(
      dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)),
      dispatch_get_main_queue(), ^{
        @strongify(self);
        if ([HUD isVisible]) {
          [LocationManager
              endUpdateLocationWithIdentifier:NSStringFromClass([self class])];
          [HUD dissmiss];
          [self loadSet:type];
        }
      });
  [LocationManager
      beginUpdateLocationWithIdentifier:NSStringFromClass([self class])
                            updateBlock:^(CLLocation *_Nullable location,
                                          NSError *_Nullable anError) {
                              dispatch_async(dispatch_get_main_queue(), ^{
                                @strongify(self);
                                [LocationManager
                                    endUpdateLocationWithIdentifier:
                                        NSStringFromClass([self class])];

                                if (location && !anError && [HUD isVisible]) {
                                  self.currentLocation = location;
                                  [LocationManager
                                      reverseGeocodeLocation:location
                                           completionHandler:^(
                                               NSArray<CLPlacemark *>
                                                   *_Nullable placemarks,
                                               NSError *_Nullable error) {
                                             if (![HUD isVisible]) {
                                               return;
                                             }

                                             CLPlacemark *place =
                                                 placemarks.firstObject;
                                             NSString *locationName = nil;
                                             if (place) {
                                               if ([place.ISOcountryCode
                                                       isEqualToString:@"CN"]) {
                                                 locationName = place.locality;
                                                 self.currentLocationName =
                                                     locationName;
                                               } else {
                                                 locationName = place.country;
                                                 self.currentLocationName =
                                                     locationName;
                                               }
                                             } else {
                                             }

                                             NSString *pushStatus =
                                                 self.tempSet.pushStatus;
                                             NSString *locationStatus = @"0";

                                             [POSTIPStatusModel
                                                 postPOSTIPStatusModel:
                                                     locationStatus
                                                 pushStatus:pushStatus
                                                 location:locationName
                                                 success:^(NSDictionary
                                                               *resultObject) {
                                                   [HUD dissmiss];
                                                   POSTIPStatusModel *member =
                                                       [POSTIPStatusModel
                                                           mj_objectWithKeyValues:
                                                               resultObject];

                                                   if ([member.success
                                                               boolValue]) {
                                                     self.tempSet
                                                         .locationStatus =
                                                         locationStatus;
                                                     self.tempSet.pushStatus =
                                                         pushStatus;
                                                     CurrentUserConfig
                                                         .enableLocation = YES;
                                                     [self.tableView
                                                             reloadData];
                                                   } else {
                                                     [self showToastFast:
                                                               member.msg];
                                                     CurrentUserConfig
                                                         .enableLocation = NO;
                                                     [self.tableView
                                                             reloadData];
                                                   }
                                                 }
                                                 failure:^(
                                                     NSError *requestErr) {
                                                   [HUD dissmiss];
                                                   [self
                                                       showToastFast:
                                                           @"数据有误,"
                                                           @"请检查网络后重试"];
                                                   CurrentUserConfig
                                                       .enableLocation = NO;
                                                   [self.tableView reloadData];
                                                 }];
                                           }];
                                } else if ([HUD isVisible]) {
                                  [HUD dissmiss];
                                  [self loadSet:type];
                                } else {
                                }
                              });
                            }];
}

- (void)preloadLocationIfAuthorized {
  CLAuthorizationStatus status = [LocationManager status];

  if (status == kCLAuthorizationStatusAuthorizedWhenInUse ||
      status == kCLAuthorizationStatusAuthorizedAlways) {
    @weakify(self);
    [LocationManager
        beginUpdateLocationWithIdentifier:@"SettingVCPreload"
                              updateBlock:^(CLLocation *_Nullable location,
                                            NSError *_Nullable anError) {
                                if (location && !anError) {
                                  dispatch_async(dispatch_get_main_queue(), ^{
                                    @strongify(self);
                                    self.currentLocation = location;

                                    [LocationManager
                                        reverseGeocodeLocation:location
                                             completionHandler:^(
                                                 NSArray<CLPlacemark *>
                                                     *_Nullable placemarks,
                                                 NSError *_Nullable error) {
                                               dispatch_async(
                                                   dispatch_get_main_queue(), ^{
                                                     @strongify(self);
                                                     [LocationManager
                                                         endUpdateLocationWithIdentifier:
                                                             @"SettingVCPreloa"
                                                             @"d"];

                                                     CLPlacemark *place =
                                                         placemarks.firstObject;
                                                     if (place) {
                                                       if ([place.ISOcountryCode
                                                               isEqualToString:
                                                                   @"CN"]) {
                                                         self.currentLocationName =
                                                             place.locality;
                                                       } else {
                                                         self.currentLocationName =
                                                             place.country;
                                                       }
                                                     } else {
                                                     }
                                                   });
                                             }];
                                  });
                                } else {
                                }
                              }];
    dispatch_after(
        dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)),
        dispatch_get_main_queue(), ^{
          [LocationManager endUpdateLocationWithIdentifier:@"SettingVCPreload"];
        });
  }
}

- (void)leftButtonClicked:(UIButton *)sender {
  self.tempSet.cellStyle = @"1";

  SettingTableViewCell *cell =
      (SettingTableViewCell *)[[sender superview] superview];
  cell.leftButton.selected = YES;
  cell.leftButton.backgroundColor = KColor_HighBlack;
  cell.rightButton.selected = NO;
  cell.rightButton.backgroundColor = [UIColor clearColor];

  [[NSUserDefaults standardUserDefaults] setObject:@"1"
                                            forKey:@"userCellStyle"];
  [[NSUserDefaults standardUserDefaults] synchronize];
  [[NSNotificationCenter defaultCenter]
      postNotificationName:kCellStyleDidChangeNotification
                    object:nil];
  [self.tableView reloadData];
  [self updateCellStyle:@"1"];
}

- (void)rightButtonClicked:(UIButton *)sender {
  self.tempSet.cellStyle = @"2";
  SettingTableViewCell *cell =
      (SettingTableViewCell *)[[sender superview] superview];
  cell.rightButton.selected = YES;
  cell.rightButton.backgroundColor = KColor_HighBlack;
  cell.leftButton.selected = NO;
  cell.leftButton.backgroundColor = [UIColor clearColor];
  [[NSUserDefaults standardUserDefaults] setObject:@"2"
                                            forKey:@"userCellStyle"];
  [[NSUserDefaults standardUserDefaults] synchronize];
  [[NSNotificationCenter defaultCenter]
      postNotificationName:kCellStyleDidChangeNotification
                    object:nil];
  [self.tableView reloadData];
  [self updateCellStyle:@"2"];
}

- (void)updateCellStyle:(NSString *)cellStyle {
  __weak typeof(self) wSelf = self;
  [HUD show];
  [CellStyleModel postCellStyleModel:cellStyle
      success:^(NSDictionary *resultObject) {
        [HUD dissmiss];
        CellStyleModel *member =
            [CellStyleModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          wSelf.tempSet.cellStyle = cellStyle;
          [[NSUserDefaults standardUserDefaults] setObject:cellStyle
                                                    forKey:@"userCellStyle"];
          [[NSUserDefaults standardUserDefaults] synchronize];
          [[NSNotificationCenter defaultCenter]
              postNotificationName:kCellStyleDidChangeNotification
                            object:nil];
        } else {
          wSelf.tempSet.cellStyle =
              [cellStyle isEqualToString:@"1"] ? @"2" : @"1";
          [wSelf.tableView reloadData];
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        wSelf.tempSet.cellStyle =
            [cellStyle isEqualToString:@"1"] ? @"2" : @"1";
        [wSelf.tableView reloadData];
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)homeHideSwitchChanged:(UISwitch *)aSwitch {
  NSString *hideValue = aSwitch.isOn ? @"1" : @"0";

  __weak typeof(self) wSelf = self;
  [HUD show];
  [HomeHideModel postHomeHideModel:hideValue
      success:^(NSDictionary *resultObject) {
        [HUD dissmiss];
        HomeHideModel *member =
            [HomeHideModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          wSelf.tempSet.homeHide = hideValue;
          [wSelf.tableView reloadData];
        } else {
          [aSwitch setOn:!aSwitch.isOn animated:YES];
          [wSelf showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        [aSwitch setOn:!aSwitch.isOn animated:YES];
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

@end
